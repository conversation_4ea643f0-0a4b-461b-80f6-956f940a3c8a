.iot-service-card {
  height: 172px;
  background: linear-gradient(
    135deg,
    rgba(20, 73, 93, 0.8) 0%,
    rgba(9, 50, 70, 0.9) 100%
  );
  padding: 16px 20px;
  box-shadow: 4px 4px 4px 0px #2499e2;
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  transition: all 0.3s ease;

  .iot-service-card-title {
    font-family: Poppins;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0%;
    color: #2499e2;
    margin-bottom: 10px;
  }

  .iot-service-card-description {
    font-family: Poppins;
    font-weight: 400;
    font-size: 16px;
    line-height: 30px;
    letter-spacing: 0%;
    color: #ffffff;
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .iot-service-card {
    width: 100%;
    max-width: 500px;
    height: auto;
    min-height: 150px;
  }
}

@media (max-width: 768px) {
  .iot-service-card {
    width: 100%;
    max-width: 100%;
    height: auto;
    min-height: 140px;
    padding: 16px;

    .iot-service-card-title {
      font-size: 18px;
      line-height: 26px;
      margin-bottom: 10px;
    }

    .iot-service-card-description {
      font-size: 14px;
      line-height: 20px;
      -webkit-line-clamp: 5;
    }
  }
}
