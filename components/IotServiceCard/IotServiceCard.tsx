"use client";

import React from "react";
import "./IotServiceCard.scss";
import { Box, Typography } from "@mui/material";

interface IotServiceCardProps {
  title: string;
  description: string;
}

const IotServiceCard: React.FC<IotServiceCardProps> = ({
  title,
  description,
}) => {
  return (
    <Box className="iot-service-card">
      <Typography className="iot-service-card-title">{title}</Typography>
      <Typography className="iot-service-card-description">
        {description}
      </Typography>
    </Box>
  );
};

export default IotServiceCard;
